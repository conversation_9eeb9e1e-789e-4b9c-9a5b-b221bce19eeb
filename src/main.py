import os
import sys
import time
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime, timedelta

# 添加项目根目录到 Python 路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

import schedule
import yaml
from dotenv import load_dotenv
from utils.logger import setup_logging, get_logger
from database.db_pool import DatabasePool

# 数据分析相关导入
ANALYSIS_DEPENDENCIES_AVAILABLE = True
WORDCLOUD_AVAILABLE = True
missing_packages = []

try:
    import pandas as pd
    import numpy as np
except ImportError as e:
    ANALYSIS_DEPENDENCIES_AVAILABLE = False
    missing_packages.append("pandas, numpy")

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.decomposition import LatentDirichletAllocation
except ImportError as e:
    ANALYSIS_DEPENDENCIES_AVAILABLE = False
    missing_packages.append("scikit-learn")

try:
    from textblob import TextBlob
except ImportError as e:
    ANALYSIS_DEPENDENCIES_AVAILABLE = False
    missing_packages.append("textblob")

try:
    import nltk
    from nltk.corpus import stopwords
    from nltk.tokenize import word_tokenize
    from nltk.stem import WordNetLemmatizer

    # 下载必要的NLTK数据
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        try:
            nltk.download('punkt', quiet=True)
        except:
            nltk.download('punkt_tab', quiet=True)

    try:
        nltk.data.find('corpora/stopwords')
    except LookupError:
        nltk.download('stopwords', quiet=True)

    try:
        nltk.data.find('corpora/wordnet')
    except LookupError:
        nltk.download('wordnet', quiet=True)

except ImportError as e:
    ANALYSIS_DEPENDENCIES_AVAILABLE = False
    missing_packages.append("nltk")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
except ImportError as e:
    ANALYSIS_DEPENDENCIES_AVAILABLE = False
    missing_packages.append("matplotlib, seaborn")

try:
    from wordcloud import WordCloud
except ImportError as e:
    WORDCLOUD_AVAILABLE = False
    missing_packages.append("wordcloud")

try:
    import requests
except ImportError as e:
    ANALYSIS_DEPENDENCIES_AVAILABLE = False
    missing_packages.append("requests")

if not ANALYSIS_DEPENDENCIES_AVAILABLE or missing_packages:
    print(f"警告: 以下数据分析依赖未安装: {', '.join(missing_packages)}")
    print("请运行以下命令安装依赖:")
    print("1. python install_dependencies.py  (推荐)")
    print("2. uv pip install -r requirements.txt")
    print("3. pip install -r requirements.txt")
    if not WORDCLOUD_AVAILABLE:
        print("注意: WordCloud安装失败不会影响其他功能，只是无法生成词云图")

# 确保日志目录存在
log_dir = Path(project_root) / 'logs'
log_dir.mkdir(exist_ok=True)

# 设置日志
setup_logging()
logger = get_logger('main')

# 加载环境变量
load_dotenv()

# 导入Reddit爬虫函数
from spider.spider_reddit import (
    save_subreddit_info,
    crawl_and_save_by_ranking
)

# 导入DAO类
from dao.subreddit_dao import SubredditDao
from dao.post_dao import PostDao
from dao.comment_dao import CommentDao

def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    try:
        config_path = Path(project_root) / 'config' / 'config.yaml'
        if not config_path.exists():
            logger.warning(f"配置文件不存在: {config_path}")
            return create_default_config()

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 替换配置中的变量
        root_dir = project_root
        for section in config:
            if isinstance(config[section], dict):
                for key, value in config[section].items():
                    if isinstance(value, str) and '%(root_dir)s' in value:
                        config[section][key] = value.replace('%(root_dir)s', root_dir)

        # 验证配置是否包含必要的部分
        if not validate_config(config):
            logger.warning("配置文件缺少必要的配置项，将使用默认配置补充")
            config = merge_with_default_config(config)

        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        return create_default_config()

def create_default_config() -> Dict[str, Any]:
    """创建默认配置"""
    return {
        'paths': {
            'cookies_path': "data/cookies/cookies.json",
            'raw_data': "data/raw",
            'processed_data': "data/processed",
            'logs': "logs/parser.log"
        },
        'schedule': {
            'daily_time': "02:00"
        },
        'logging': {
            'config_file': "config/logging.yaml"
        },
        'reddit': {
            'enabled': True,
            'subreddits': [
                'SaaS'
            ],
            'posts_limit': 5,
            'comments_limit': 10,
            'rankings': ['hot', 'new', 'top'],
            'time_filters': {
                'top': 'week',
                'controversial': 'day'
            },
            'batch_size': 5,
            'delay_between_subreddits': 3,
            'delay_between_batches': 10
        }
    }

def validate_config(config: Dict[str, Any]) -> bool:
    """验证配置是否包含必要的部分"""
    required_sections = ['paths', 'schedule', 'logging']
    for section in required_sections:
        if section not in config:
            return False

    # 验证Reddit配置（可选）
    if 'reddit' in config:
        reddit_config = config['reddit']
        required_reddit_keys = ['enabled', 'subreddits', 'posts_limit', 'comments_limit']
        for key in required_reddit_keys:
            if key not in reddit_config:
                logger.warning(f"Reddit配置缺少必要字段: {key}")
                return False

    return True

def merge_with_default_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """将配置与默认配置合并"""
    default_config = create_default_config()

    # 合并顶级部分
    for section in default_config:
        if section not in config:
            config[section] = default_config[section]
        elif isinstance(default_config[section], dict):
            # 合并子部分
            for key in default_config[section]:
                if key not in config[section]:
                    config[section][key] = default_config[section][key]

    return config

# 加载配置
CONFIG = load_config()

# Notion配置
NOTION_CONFIG = CONFIG.get('notion', {})
NOTION_API_KEY = NOTION_CONFIG.get('api_key', os.getenv('NOTION_API_KEY', ''))
NOTION_DATABASE_ID = NOTION_CONFIG.get('database_id', os.getenv('NOTION_DATABASE_ID', ''))

def verify_environment_variables() -> bool:
    """验证必要的环境变量是否已设置"""
    required_vars = {
        'DB_HOST': '数据库主机',
        'DB_PORT': '数据库端口',
        'DB_USER': '数据库用户名',
        'DB_PASSWORD': '数据库密码',
        'DB_NAME': '数据库名称',
        'REDDIT_CLIENT_ID': 'Reddit客户端ID',
        'REDDIT_CLIENT_SECRET': 'Reddit客户端密钥'
    }

    missing_vars = []
    for var, desc in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({desc})")

    if missing_vars:
        logger.error("缺少以下必要的环境变量:")
        for var in missing_vars:
            logger.error(f"  - {var}")
        return False

    return True

def setup_database() -> bool:
    """初始化数据库连接和表结构"""
    try:
        db_pool = DatabasePool()
        conn = db_pool.get_connection()

        # PostgreSQL不允许在事务中创建数据库，需要先关闭自动提交
        conn.autocommit = True
        cursor = conn.cursor()

        # 检查数据库是否存在
        db_name = os.getenv('DB_NAME')
        if not db_name:
            logger.error("数据库名称未配置")
            return False

        cursor.execute("""
            SELECT 1 FROM pg_database WHERE datname = %s
        """, (db_name,))

        # 如果数据库不存在则创建
        if not cursor.fetchone():
            cursor.execute(f'CREATE DATABASE {db_name}')
            logger.info(f"数据库 {db_name} 创建成功")

        # 关闭当前连接
        cursor.close()
        conn.close()

        # 重新连接到新创建的数据库
        conn = db_pool.get_connection()
        cursor = conn.cursor()
        
        # 创建Reddit相关表，分开执行每条语句，确保错误不会影响整个过程
        try:
            # 创建Reddit帖子表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS reddit_posts (
                id SERIAL PRIMARY KEY, -- 自增主键
                post_id VARCHAR(20) NOT NULL UNIQUE, -- Reddit帖子ID，唯一
                subreddit VARCHAR(100) NOT NULL, -- 所属子版块名称
                title VARCHAR(300) NOT NULL, -- 帖子标题
                author VARCHAR(100), -- 作者用户名
                created_utc TIMESTAMP, -- 发布时间
                score INTEGER DEFAULT 0, -- 评分/得分
                upvote_ratio FLOAT, -- 赞成率
                url TEXT, -- 原始URL
                permalink TEXT, -- Reddit永久链接
                num_comments INTEGER DEFAULT 0, -- 评论数量
                is_self BOOLEAN DEFAULT FALSE, -- 是否为自发帖子
                selftext TEXT, -- 帖子文本内容
                is_original_content BOOLEAN DEFAULT FALSE, -- 是否为原创内容
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 记录更新时间
            )
            """)
            
            logger.info("Reddit帖子表创建成功")
            
            # 创建帖子表索引
            try:
                cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_post_subreddit ON reddit_posts(subreddit);
                CREATE INDEX IF NOT EXISTS idx_post_author ON reddit_posts(author);
                CREATE INDEX IF NOT EXISTS idx_post_created ON reddit_posts(created_utc);
                CREATE INDEX IF NOT EXISTS idx_post_score ON reddit_posts(score)
                """)
                
                logger.info("Reddit帖子表索引创建成功")
            except Exception as e:
                logger.warning(f"创建帖子表索引失败，但将继续: {e}")
            
            # 添加帖子表注释
            try:
                cursor.execute("""
                COMMENT ON TABLE reddit_posts IS 'Reddit帖子信息表';
                COMMENT ON COLUMN reddit_posts.id IS '主键ID';
                COMMENT ON COLUMN reddit_posts.post_id IS 'Reddit帖子ID';
                COMMENT ON COLUMN reddit_posts.subreddit IS '所属子版块名称';
                COMMENT ON COLUMN reddit_posts.title IS '帖子标题';
                COMMENT ON COLUMN reddit_posts.author IS '作者用户名';
                COMMENT ON COLUMN reddit_posts.created_utc IS '发布时间';
                COMMENT ON COLUMN reddit_posts.score IS '评分/得分';
                COMMENT ON COLUMN reddit_posts.upvote_ratio IS '赞成率';
                COMMENT ON COLUMN reddit_posts.url IS '原始URL';
                COMMENT ON COLUMN reddit_posts.permalink IS 'Reddit永久链接';
                COMMENT ON COLUMN reddit_posts.num_comments IS '评论数量';
                COMMENT ON COLUMN reddit_posts.is_self IS '是否为自发帖子';
                COMMENT ON COLUMN reddit_posts.selftext IS '帖子文本内容';
                COMMENT ON COLUMN reddit_posts.is_original_content IS '是否为原创内容';
                COMMENT ON COLUMN reddit_posts.created_at IS '记录创建时间';
                COMMENT ON COLUMN reddit_posts.updated_at IS '记录更新时间'
                """)
                
                logger.info("Reddit帖子表注释添加成功")
            except Exception as e:
                logger.warning(f"添加帖子表注释失败，但将继续: {e}")
                
        except Exception as e:
            logger.warning(f"创建Reddit帖子表失败，但将继续: {e}")
        
        try:
            # 创建Reddit评论表
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS reddit_comments (
                id SERIAL PRIMARY KEY, -- 自增主键
                comment_id VARCHAR(20) NOT NULL UNIQUE, -- Reddit评论ID，唯一
                post_id VARCHAR(20) NOT NULL, -- 关联的帖子ID
                author VARCHAR(100), -- 评论作者
                body TEXT, -- 评论内容
                score INTEGER DEFAULT 0, -- 评论得分
                created_utc TIMESTAMP, -- 发布时间
                is_submitter BOOLEAN DEFAULT FALSE, -- 是否为原帖作者
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 记录创建时间
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 记录更新时间
            )
            """)
            
            logger.info("Reddit评论表创建成功")
            
            # 稍后添加外键约束
            try:
                cursor.execute("""
                ALTER TABLE reddit_comments
                ADD CONSTRAINT fk_comment_post 
                FOREIGN KEY (post_id) REFERENCES reddit_posts(post_id) ON DELETE CASCADE
                """)
                
                logger.info("Reddit评论表外键约束添加成功")
            except Exception as e:
                logger.warning(f"添加评论表外键约束失败，但将继续: {e}")
            
            # 创建评论表索引
            try:
                cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_comment_post_id ON reddit_comments(post_id);
                CREATE INDEX IF NOT EXISTS idx_comment_author ON reddit_comments(author);
                CREATE INDEX IF NOT EXISTS idx_comment_created ON reddit_comments(created_utc);
                CREATE INDEX IF NOT EXISTS idx_comment_score ON reddit_comments(score)
                """)
                
                logger.info("Reddit评论表索引创建成功")
            except Exception as e:
                logger.warning(f"创建评论表索引失败，但将继续: {e}")
            
            # 添加评论表注释
            try:
                cursor.execute("""
                COMMENT ON TABLE reddit_comments IS 'Reddit评论信息表';
                COMMENT ON COLUMN reddit_comments.id IS '主键ID';
                COMMENT ON COLUMN reddit_comments.comment_id IS 'Reddit评论ID';
                COMMENT ON COLUMN reddit_comments.post_id IS '关联的帖子ID';
                COMMENT ON COLUMN reddit_comments.author IS '评论作者';
                COMMENT ON COLUMN reddit_comments.body IS '评论内容';
                COMMENT ON COLUMN reddit_comments.score IS '评论得分';
                COMMENT ON COLUMN reddit_comments.created_utc IS '发布时间';
                COMMENT ON COLUMN reddit_comments.is_submitter IS '是否为原帖作者';
                COMMENT ON COLUMN reddit_comments.created_at IS '记录创建时间';
                COMMENT ON COLUMN reddit_comments.updated_at IS '记录更新时间'
                """)
                
                logger.info("Reddit评论表注释添加成功")
            except Exception as e:
                logger.warning(f"添加评论表注释失败，但将继续: {e}")
                
        except Exception as e:
            logger.warning(f"创建Reddit评论表失败，但将继续: {e}")
            
        try:
            # 创建触发器函数
            cursor.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql'
            """)
            
            logger.info("更新时间触发器函数创建成功")
            
            # 为帖子表添加触发器
            try:
                cursor.execute("""
                DROP TRIGGER IF EXISTS update_reddit_posts_updated_at ON reddit_posts;
                CREATE TRIGGER update_reddit_posts_updated_at
                    BEFORE UPDATE ON reddit_posts
                    FOR EACH ROW
                    EXECUTE FUNCTION update_updated_at_column()
                """)
                
                logger.info("帖子表更新时间触发器创建成功")
            except Exception as e:
                logger.warning(f"创建帖子表触发器失败，但将继续: {e}")
            
            # 为评论表添加触发器
            try:
                cursor.execute("""
                DROP TRIGGER IF EXISTS update_reddit_comments_updated_at ON reddit_comments;
                CREATE TRIGGER update_reddit_comments_updated_at
                    BEFORE UPDATE ON reddit_comments
                    FOR EACH ROW
                    EXECUTE FUNCTION update_updated_at_column()
                """)
                
                logger.info("评论表更新时间触发器创建成功")
            except Exception as e:
                logger.warning(f"创建评论表触发器失败，但将继续: {e}")
                
        except Exception as e:
            logger.warning(f"创建更新时间触发器失败，但将继续: {e}")
        
        # 我们不在这里创建子版块表，而是依赖SubredditDao类来创建
        
        conn.commit()
        logger.info("数据库初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        return False
    finally:
        if 'conn' in locals() and 'cursor' in locals():
            DatabasePool.close_connection(conn, cursor)

def setup_logging_from_config():
    """从配置文件设置日志"""
    try:
        # 获取项目根目录
        root_dir = Path(project_root)
        
        # 确保日志目录存在
        log_dir = root_dir / 'logs'
        log_dir.mkdir(exist_ok=True)
        
        # 加载日志配置文件
        logging_config_path = root_dir / 'config' / 'logging.yaml'
        if logging_config_path.exists():
            with open(logging_config_path, 'r', encoding='utf-8') as f:
                log_config = yaml.safe_load(f)
            
            # 替换配置中的路径变量
            if 'handlers' in log_config:
                for handler_config in log_config['handlers'].values():
                    if 'filename' in handler_config:
                        # 将相对路径转换为绝对路径
                        filename = handler_config['filename']
                        if not os.path.isabs(filename):
                            handler_config['filename'] = str(root_dir / filename)
                        
                        # 确保日志文件的目录存在
                        os.makedirs(os.path.dirname(handler_config['filename']), exist_ok=True)
            
            # 配置日志
            import logging.config
            logging.config.dictConfig(log_config)
            logging.info("日志配置已从配置文件加载")
        else:
            # 使用默认日志设置
            setup_logging()
            logging.warning(f"日志配置文件不存在: {logging_config_path}")
    except Exception as e:
        # 使用备选日志设置
        setup_logging()
        logging.error(f"加载日志配置失败: {str(e)}")

def run_spider() -> bool:
    """执行爬虫任务"""
    try:
        logger.info("开始执行爬虫任务")

        # 获取Reddit配置
        reddit_config = CONFIG.get("reddit", {})
        if not reddit_config.get("enabled", False):
            logger.info("Reddit爬虫已禁用，跳过执行")
            return True

        logger.info("正在启动Reddit爬虫")

        # 确保数据表已创建
        SubredditDao.create_table_if_not_exists()
        PostDao.create_table_if_not_exists()
        CommentDao.create_table_if_not_exists()

        # 获取配置参数
        all_subreddits = reddit_config.get("subreddits", [])
        posts_limit = reddit_config.get("posts_limit", 5)
        comments_limit = reddit_config.get("comments_limit", 10)
        batch_size = reddit_config.get("batch_size", 5)
        delay_between_subreddits = reddit_config.get("delay_between_subreddits", 3)
        delay_between_batches = reddit_config.get("delay_between_batches", 10)
        time_filter = reddit_config.get("time_filters", {}).get("top", "week")

        if not all_subreddits:
            logger.warning("未配置要抓取的子版块，跳过执行")
            return True

        # 分批处理子版块
        total_batches = (len(all_subreddits) + batch_size - 1) // batch_size
        logger.info(f"共有 {len(all_subreddits)} 个子版块，将分 {total_batches} 批处理")

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(all_subreddits))
            batch_subreddits = all_subreddits[start_idx:end_idx]

            logger.info(f"开始处理第 {batch_idx + 1}/{total_batches} 批子版块")

            for subreddit_name in batch_subreddits:
                try:
                    logger.info(f"正在抓取子版块: {subreddit_name}")

                    # 首先保存子版块信息（包括排名和URL）
                    save_subreddit_info(subreddit_name)

                    # 抓取热门帖子
                    hot_result = crawl_and_save_by_ranking(
                        subreddit_name,
                        "hot",
                        posts_limit,
                        comments_limit
                    )

                    # 抓取最新帖子
                    new_result = crawl_and_save_by_ranking(
                        subreddit_name,
                        "new",
                        posts_limit,
                        comments_limit
                    )

                    # 抓取本周最佳帖子
                    top_result = crawl_and_save_by_ranking(
                        subreddit_name,
                        "top",
                        posts_limit,
                        comments_limit,
                        time_filter
                    )

                    logger.info(f"子版块 {subreddit_name} 抓取完成:")
                    logger.info(f"- 热门帖子: 已保存 {hot_result['posts_saved']} 条")
                    logger.info(f"- 最新帖子: 已保存 {new_result['posts_saved']} 条")
                    logger.info(f"- 本周最佳: 已保存 {top_result['posts_saved']} 条")

                    # 每个子版块处理完后暂停，避免请求过于频繁
                    time.sleep(delay_between_subreddits)

                except Exception as e:
                    logger.error(f"抓取子版块 {subreddit_name} 时出错: {str(e)}")

            # 每批处理完后暂停
            if batch_idx < total_batches - 1:
                logger.info(f"第 {batch_idx + 1} 批处理完成，暂停{delay_between_batches}秒后继续下一批")
                time.sleep(delay_between_batches)

        logger.info("所有子版块抓取完成")
        return True

    except Exception as e:
        logger.error(f"Reddit爬虫执行出错: {str(e)}")
        return False

def schedule_tasks() -> None:
    """设置定时任务"""
    # 从配置文件获取定时设置，默认每天凌晨2点
    schedule_config = CONFIG.get('schedule', {})
    schedule_time = schedule_config.get('daily_time', "02:00")

    # 设置定时任务
    schedule.every().day.at(schedule_time).do(run_spider)

    logger.info(f"定时任务已设置，将在每天 {schedule_time} 执行Reddit爬虫")

    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次是否有待执行的任务
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止定时任务...")
    except Exception as e:
        logger.error(f"定时任务执行出错: {str(e)}")
        raise

def ensure_data_directories() -> None:
    """确保数据目录存在"""
    data_dirs = ['data', 'logs', 'data/raw', 'data/processed']
    for dir_name in data_dirs:
        try:
            os.makedirs(dir_name, exist_ok=True)
            logger.debug(f"确保目录存在: {dir_name}")
        except OSError as e:
            logger.error(f"创建目录失败 {dir_name}: {str(e)}")
            raise

def initialize_dao_tables() -> bool:
    """初始化DAO相关的数据表"""
    try:
        # 创建数据表
        subreddit_result = SubredditDao.create_table_if_not_exists()
        post_result = PostDao.create_table_if_not_exists()
        comment_result = CommentDao.create_table_if_not_exists()

        if subreddit_result and post_result and comment_result:
            logger.info("所有Reddit数据表初始化成功")
            return True
        else:
            # 尝试采用错误恢复策略
            logger.warning("部分Reddit数据表初始化失败，尝试修复或自适应处理")
            
            # 如果子版块表创建失败，可能是因为缺少新增字段，尝试更新表结构
            if not subreddit_result:
                try:
                    logger.info("尝试手动修复子版块表结构")
                    conn = DatabasePool().get_connection()
                    cursor = conn.cursor()
                    
                    # 检查是否缺少rank列
                    cursor.execute("""
                    SELECT column_name FROM information_schema.columns 
                    WHERE table_name = 'reddit_subreddits' AND column_name = 'rank'
                    """)
                    
                    # 如果缺少rank列，添加它
                    if not cursor.fetchone():
                        logger.info("添加缺失的rank列")
                        cursor.execute("ALTER TABLE reddit_subreddits ADD COLUMN rank INTEGER")
                        cursor.execute("CREATE INDEX IF NOT EXISTS idx_sub_rank ON reddit_subreddits(rank)")
                        cursor.execute("COMMENT ON COLUMN reddit_subreddits.rank IS '子版块排名'")
                    
                    # 检查是否缺少url列
                    cursor.execute("""
                    SELECT column_name FROM information_schema.columns 
                    WHERE table_name = 'reddit_subreddits' AND column_name = 'url'
                    """)
                    
                    # 如果缺少url列，添加它
                    if not cursor.fetchone():
                        logger.info("添加缺失的url列")
                        cursor.execute("ALTER TABLE reddit_subreddits ADD COLUMN url TEXT")
                        cursor.execute("COMMENT ON COLUMN reddit_subreddits.url IS '子版块URL地址'")
                    
                    conn.commit()
                    cursor.close()
                    conn.close()
                    
                    logger.info("子版块表结构修复完成")
                    return True
                except Exception as e:
                    logger.error(f"修复子版块表结构失败: {str(e)}")
                    return False
            
            return False
    except Exception as e:
        logger.error(f"初始化DAO数据表失败: {str(e)}")
        return False

def main() -> None:
    """主函数"""
    try:
        logger.info("Reddit爬虫程序启动")

        # 验证环境变量
        if not verify_environment_variables():
            logger.error("环境变量配置不完整，程序退出")
            sys.exit(1)

        # 确保数据目录存在
        ensure_data_directories()

        # 检查配置是否加载成功
        if CONFIG is None:
            logger.error("配置加载失败，程序退出")
            sys.exit(1)

        # 初始化数据库
        if not setup_database():
            logger.error("数据库初始化失败，程序退出")
            sys.exit(1)

        # 初始化DAO数据表
        if not initialize_dao_tables():
            logger.error("DAO数据表初始化失败，程序退出")
            sys.exit(1)

        # 解析命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] == "--now":
                # 立即执行一次爬虫
                success = run_spider()
                if success:
                    logger.info("爬虫执行完成")
                else:
                    logger.error("爬虫执行失败")
                    sys.exit(1)
            elif sys.argv[1] == "--analyze":
                # 立即执行一次痛点分析
                success = run_pain_point_analysis()
                if success:
                    logger.info("痛点分析执行完成")
                else:
                    logger.error("痛点分析执行失败")
                    sys.exit(1)
            elif sys.argv[1] == "--all":
                # 执行爬虫和痛点分析
                spider_success = run_spider()
                analysis_success = run_pain_point_analysis()
                if spider_success and analysis_success:
                    logger.info("爬虫和痛点分析执行完成")
                else:
                    logger.error("部分任务执行失败")
                    sys.exit(1)
            else:
                logger.error(f"未知的命令行参数: {sys.argv[1]}")
                logger.info("可用参数: --now (仅爬虫), --analyze (仅分析), --all (爬虫+分析)")
                sys.exit(1)
        else:
            # 启动定时任务
            schedule_tasks()

        logger.info("程序结束")

    except KeyboardInterrupt:
        logger.info("接收到中断信号，程序正常退出")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        sys.exit(1)


class PainPointAnalyzer:
    """用户痛点分析器"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化痛点分析器

        参数:
            config: 配置字典
        """
        self.config = config
        self.analysis_config = config.get('pain_point_analysis', {})
        self.ollama_config = self.analysis_config.get('ollama', {})
        self.logger = get_logger('pain_point_analyzer')

        # 检查依赖是否可用
        if not ANALYSIS_DEPENDENCIES_AVAILABLE:
            raise ImportError("数据分析依赖未安装，请运行: pip install -r requirements.txt")

        # 初始化NLTK组件
        self.stop_words = set(stopwords.words('english'))
        self.lemmatizer = WordNetLemmatizer()

        # 创建输出目录
        self._create_output_directories()

    def _create_output_directories(self):
        """创建输出目录"""
        try:
            results_path = Path(self.analysis_config.get('output', {}).get('results_path', 'data/processed/pain_point_analysis'))
            charts_path = Path(self.analysis_config.get('output', {}).get('charts_path', 'data/processed/charts'))

            results_path.mkdir(parents=True, exist_ok=True)
            charts_path.mkdir(parents=True, exist_ok=True)

            self.results_path = results_path
            self.charts_path = charts_path

        except Exception as e:
            self.logger.error(f"创建输出目录失败: {e}")
            raise

    def preprocess_text(self, text: str) -> str:
        """
        文本预处理

        参数:
            text: 原始文本

        返回:
            预处理后的文本
        """
        if not text or not isinstance(text, str):
            return ""

        try:
            # 转换为小写
            text = text.lower()

            # 移除URL
            text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)

            # 移除特殊字符，保留字母、数字和空格
            text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)

            # 分词
            tokens = word_tokenize(text)

            # 移除停用词和短词
            tokens = [token for token in tokens if token not in self.stop_words and len(token) > 2]

            # 词形还原
            tokens = [self.lemmatizer.lemmatize(token) for token in tokens]

            return ' '.join(tokens)

        except Exception as e:
            self.logger.error(f"文本预处理失败: {e}")
            return ""

    def get_comments_data(self, days: int = 7, limit: int = None) -> List[Dict]:
        """
        从数据库获取评论数据

        参数:
            days: 获取最近几天的数据
            limit: 限制数量

        返回:
            评论数据列表
        """
        try:
            from dao.comment_dao import CommentDao

            conn = DatabasePool().get_connection()
            cursor = conn.cursor()

            # 计算日期范围
            date_threshold = datetime.now() - timedelta(days=days)

            # 构建查询
            query = """
            SELECT
                c.comment_id, c.post_id, c.author, c.body, c.score,
                c.created_utc, c.is_submitter,
                p.title as post_title, p.subreddit, p.selftext
            FROM reddit_comments c
            JOIN reddit_posts p ON c.post_id = p.post_id
            WHERE c.created_utc > %s
                AND c.body IS NOT NULL
                AND LENGTH(c.body) >= %s
            ORDER BY c.created_utc DESC
            """

            params = [date_threshold, self.analysis_config.get('analysis', {}).get('min_comment_length', 10)]

            if limit:
                query += " LIMIT %s"
                params.append(limit)

            cursor.execute(query, params)
            results = cursor.fetchall()

            comments = []
            for row in results:
                comments.append({
                    'comment_id': row[0],
                    'post_id': row[1],
                    'author': row[2],
                    'body': row[3],
                    'score': row[4],
                    'created_utc': row[5],
                    'is_submitter': row[6],
                    'post_title': row[7],
                    'subreddit': row[8],
                    'post_selftext': row[9]
                })

            self.logger.info(f"获取到 {len(comments)} 条评论数据")
            return comments

        except Exception as e:
            self.logger.error(f"获取评论数据失败: {e}")
            return []
        finally:
            if 'conn' in locals() and 'cursor' in locals():
                DatabasePool.close_connection(conn, cursor)

    def perform_sentiment_analysis(self, comments: List[Dict]) -> List[Dict]:
        """
        执行情感分析

        参数:
            comments: 评论数据列表

        返回:
            包含情感分析结果的评论列表
        """
        try:
            self.logger.info("开始执行情感分析...")

            for comment in comments:
                if comment.get('body'):
                    # 使用TextBlob进行情感分析
                    blob = TextBlob(comment['body'])
                    sentiment = blob.sentiment

                    comment['sentiment_polarity'] = sentiment.polarity  # -1到1，负值表示负面情感
                    comment['sentiment_subjectivity'] = sentiment.subjectivity  # 0到1，主观性

                    # 分类情感
                    if sentiment.polarity < -0.1:
                        comment['sentiment_label'] = 'negative'
                    elif sentiment.polarity > 0.1:
                        comment['sentiment_label'] = 'positive'
                    else:
                        comment['sentiment_label'] = 'neutral'

            self.logger.info("情感分析完成")
            return comments

        except Exception as e:
            self.logger.error(f"情感分析失败: {e}")
            return comments

    def extract_keywords(self, comments: List[Dict]) -> List[Tuple[str, float]]:
        """
        提取关键词

        参数:
            comments: 评论数据列表

        返回:
            关键词及其重要性分数的列表
        """
        try:
            self.logger.info("开始提取关键词...")

            # 预处理所有评论文本
            processed_texts = []
            for comment in comments:
                if comment.get('body'):
                    processed_text = self.preprocess_text(comment['body'])
                    if processed_text:
                        processed_texts.append(processed_text)

            if not processed_texts:
                self.logger.warning("没有有效的文本数据用于关键词提取")
                return []

            # 使用TF-IDF提取关键词
            vectorizer = TfidfVectorizer(
                max_features=self.analysis_config.get('analysis', {}).get('top_keywords_count', 20),
                min_df=self.analysis_config.get('analysis', {}).get('min_word_frequency', 2),
                ngram_range=(1, 2)  # 包括单词和双词组合
            )

            tfidf_matrix = vectorizer.fit_transform(processed_texts)
            feature_names = vectorizer.get_feature_names_out()

            # 计算每个词的平均TF-IDF分数
            mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)

            # 创建关键词-分数对
            keywords = [(feature_names[i], mean_scores[i]) for i in range(len(feature_names))]
            keywords.sort(key=lambda x: x[1], reverse=True)

            self.logger.info(f"提取到 {len(keywords)} 个关键词")
            return keywords

        except Exception as e:
            self.logger.error(f"关键词提取失败: {e}")
            return []

    def perform_topic_modeling(self, comments: List[Dict]) -> Tuple[List[List[Tuple[str, float]]], List[str]]:
        """
        执行主题建模

        参数:
            comments: 评论数据列表

        返回:
            主题词汇列表和主题标签列表
        """
        try:
            self.logger.info("开始执行主题建模...")

            # 预处理文本
            processed_texts = []
            for comment in comments:
                if comment.get('body'):
                    processed_text = self.preprocess_text(comment['body'])
                    if processed_text:
                        processed_texts.append(processed_text)

            if len(processed_texts) < 5:
                self.logger.warning("文本数量太少，无法进行有效的主题建模")
                return [], []

            # 使用TF-IDF向量化
            vectorizer = TfidfVectorizer(
                max_features=100,
                min_df=2,
                max_df=0.8,
                ngram_range=(1, 2)
            )

            tfidf_matrix = vectorizer.fit_transform(processed_texts)
            feature_names = vectorizer.get_feature_names_out()

            # LDA主题建模
            n_topics = min(self.analysis_config.get('analysis', {}).get('lda_topics_count', 5), len(processed_texts) // 2)
            lda = LatentDirichletAllocation(
                n_components=n_topics,
                random_state=42,
                max_iter=10
            )

            lda.fit(tfidf_matrix)

            # 提取主题词汇
            topics = []
            topic_labels = []

            for topic_idx, topic in enumerate(lda.components_):
                # 获取每个主题的前10个词
                top_words_idx = topic.argsort()[-10:][::-1]
                topic_words = [(feature_names[i], topic[i]) for i in top_words_idx]
                topics.append(topic_words)

                # 生成主题标签（使用前3个词）
                top_3_words = [feature_names[i] for i in top_words_idx[:3]]
                topic_labels.append(f"主题{topic_idx + 1}: {', '.join(top_3_words)}")

            self.logger.info(f"识别出 {len(topics)} 个主题")
            return topics, topic_labels

        except Exception as e:
            self.logger.error(f"主题建模失败: {e}")
            return [], []

    def call_ollama_for_business_solutions(self, analysis_summary: Dict) -> str:
        """
        调用Ollama模型生成商业化解决方案

        参数:
            analysis_summary: 分析摘要数据

        返回:
            商业化解决方案文本
        """
        try:
            self.logger.info("正在调用Ollama生成商业化解决方案...")

            # 构建提示词
            prompt = self._build_business_solution_prompt(analysis_summary)

            # 调用Ollama API
            ollama_url = f"{self.ollama_config.get('base_url', 'http://localhost:11434')}/api/generate"

            payload = {
                "model": self.ollama_config.get('model', 'gemma2:latest'),
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 2000
                }
            }

            response = requests.post(
                ollama_url,
                json=payload,
                timeout=self.ollama_config.get('timeout', 60)
            )

            if response.status_code == 200:
                result = response.json()
                business_solution = result.get('response', '')
                self.logger.info("成功生成商业化解决方案")
                return business_solution
            else:
                self.logger.error(f"Ollama API调用失败: {response.status_code} - {response.text}")
                return "无法生成商业化解决方案：API调用失败"

        except Exception as e:
            self.logger.error(f"调用Ollama失败: {e}")
            return f"无法生成商业化解决方案：{str(e)}"

    def _build_business_solution_prompt(self, analysis_summary: Dict) -> str:
        """
        构建商业化解决方案的提示词

        参数:
            analysis_summary: 分析摘要

        返回:
            提示词文本
        """
        negative_comments = analysis_summary.get('negative_comments', [])
        top_keywords = analysis_summary.get('top_keywords', [])
        topics = analysis_summary.get('topic_labels', [])

        prompt = f"""
作为一名资深的商业分析师和产品经理，请基于以下用户评论分析数据，识别用户痛点并提出具体的商业化解决方案。

## 分析数据摘要：

### 负面情感评论数量：{len(negative_comments)}
### 主要关键词：
{chr(10).join([f"- {keyword[0]} (重要性: {keyword[1]:.3f})" for keyword in top_keywords[:10]])}

### 识别的主题：
{chr(10).join([f"- {topic}" for topic in topics])}

### 典型负面评论示例：
{chr(10).join([f"- {comment['body'][:200]}..." for comment in negative_comments[:5]])}

## 请提供以下分析：

1. **用户痛点识别**：
   - 基于关键词和负面评论，识别出3-5个主要用户痛点
   - 每个痛点要具体描述问题的本质和影响

2. **商业化解决方案**：
   - 针对每个痛点，提出2-3个具体的产品或服务解决方案
   - 说明解决方案的实施方式和预期效果
   - 评估解决方案的商业价值和市场机会

3. **优先级建议**：
   - 根据痛点的严重程度和解决方案的可行性，给出优先级排序
   - 提供实施建议和时间规划

请用中文回答，结构清晰，内容具体可操作。
"""
        return prompt

    def generate_visualizations(self, comments: List[Dict], keywords: List[Tuple[str, float]],
                              topics: List[str], analysis_summary: Dict):
        """
        生成可视化图表

        参数:
            comments: 评论数据
            keywords: 关键词列表
            topics: 主题列表
            analysis_summary: 分析摘要
        """
        try:
            if not self.analysis_config.get('output', {}).get('generate_visualizations', True):
                return

            self.logger.info("开始生成可视化图表...")

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 1. 情感分布图
            self._create_sentiment_distribution_chart(comments)

            # 2. 关键词词云
            self._create_keywords_wordcloud(keywords)

            # 3. 主题分布图
            self._create_topic_distribution_chart(topics, comments)

            # 4. 时间序列情感趋势图
            self._create_sentiment_timeline(comments)

            self.logger.info("可视化图表生成完成")

        except Exception as e:
            self.logger.error(f"生成可视化图表失败: {e}")

    def _create_sentiment_distribution_chart(self, comments: List[Dict]):
        """创建情感分布图"""
        try:
            sentiment_counts = {'positive': 0, 'neutral': 0, 'negative': 0}

            for comment in comments:
                label = comment.get('sentiment_label', 'neutral')
                sentiment_counts[label] += 1

            plt.figure(figsize=(10, 6))
            colors = ['#2ecc71', '#f39c12', '#e74c3c']
            labels = ['积极', '中性', '消极']
            values = [sentiment_counts['positive'], sentiment_counts['neutral'], sentiment_counts['negative']]

            plt.pie(values, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            plt.title('评论情感分布', fontsize=16, fontweight='bold')
            plt.axis('equal')

            plt.tight_layout()
            plt.savefig(self.charts_path / 'sentiment_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            self.logger.error(f"创建情感分布图失败: {e}")

    def _create_keywords_wordcloud(self, keywords: List[Tuple[str, float]]):
        """创建关键词词云"""
        try:
            if not keywords:
                return

            if not WORDCLOUD_AVAILABLE:
                self.logger.warning("WordCloud库不可用，跳过词云生成")
                # 创建一个简单的关键词条形图作为替代
                self._create_keywords_bar_chart(keywords)
                return

            # 准备词云数据
            word_freq = {word: score for word, score in keywords[:50]}

            # 创建词云
            wordcloud = WordCloud(
                width=1200,
                height=600,
                background_color='white',
                max_words=50,
                colormap='viridis',
                font_path=None  # 使用默认字体
            ).generate_from_frequencies(word_freq)

            plt.figure(figsize=(15, 8))
            plt.imshow(wordcloud, interpolation='bilinear')
            plt.axis('off')
            plt.title('关键词词云', fontsize=16, fontweight='bold')

            plt.tight_layout()
            plt.savefig(self.charts_path / 'keywords_wordcloud.png', dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            self.logger.error(f"创建关键词词云失败: {e}")
            # 如果词云失败，创建条形图作为备选
            self._create_keywords_bar_chart(keywords)

    def _create_keywords_bar_chart(self, keywords: List[Tuple[str, float]]):
        """创建关键词条形图（词云的备选方案）"""
        try:
            if not keywords:
                return

            # 取前20个关键词
            top_keywords = keywords[:20]
            words = [word for word, score in top_keywords]
            scores = [score for word, score in top_keywords]

            plt.figure(figsize=(12, 8))
            bars = plt.barh(range(len(words)), scores, color='skyblue', alpha=0.7)
            plt.yticks(range(len(words)), words)
            plt.xlabel('重要性分数', fontsize=12)
            plt.title('关键词重要性排名', fontsize=16, fontweight='bold')
            plt.gca().invert_yaxis()  # 最重要的在顶部

            # 添加数值标签
            for i, (bar, score) in enumerate(zip(bars, scores)):
                plt.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2,
                        f'{score:.3f}', ha='left', va='center', fontsize=10)

            plt.tight_layout()
            plt.savefig(self.charts_path / 'keywords_bar_chart.png', dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            self.logger.error(f"创建关键词条形图失败: {e}")

    def _create_topic_distribution_chart(self, topics: List[str], comments: List[Dict]):
        """创建主题分布图"""
        try:
            if not topics:
                return

            plt.figure(figsize=(12, 8))

            # 简单的主题分布（这里使用主题数量作为示例）
            topic_counts = [len(topics[i].split(',')) for i in range(len(topics))]

            plt.bar(range(len(topics)), topic_counts, color='skyblue', alpha=0.7)
            plt.xlabel('主题', fontsize=12)
            plt.ylabel('相关词汇数量', fontsize=12)
            plt.title('主题分布', fontsize=16, fontweight='bold')
            plt.xticks(range(len(topics)), [f'主题{i+1}' for i in range(len(topics))], rotation=45)

            plt.tight_layout()
            plt.savefig(self.charts_path / 'topic_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            self.logger.error(f"创建主题分布图失败: {e}")

    def _create_sentiment_timeline(self, comments: List[Dict]):
        """创建情感时间序列图"""
        try:
            # 按日期分组计算平均情感
            daily_sentiment = {}

            for comment in comments:
                if comment.get('created_utc') and comment.get('sentiment_polarity') is not None:
                    date = comment['created_utc'].date()
                    if date not in daily_sentiment:
                        daily_sentiment[date] = []
                    daily_sentiment[date].append(comment['sentiment_polarity'])

            if not daily_sentiment:
                return

            # 计算每日平均情感
            dates = sorted(daily_sentiment.keys())
            avg_sentiments = [np.mean(daily_sentiment[date]) for date in dates]

            plt.figure(figsize=(14, 6))
            plt.plot(dates, avg_sentiments, marker='o', linewidth=2, markersize=4)
            plt.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
            plt.xlabel('日期', fontsize=12)
            plt.ylabel('平均情感极性', fontsize=12)
            plt.title('情感趋势时间序列', fontsize=16, fontweight='bold')
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(self.charts_path / 'sentiment_timeline.png', dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            self.logger.error(f"创建情感时间序列图失败: {e}")

    def save_analysis_results(self, analysis_results: Dict):
        """
        保存分析结果到文件

        参数:
            analysis_results: 分析结果字典
        """
        try:
            if not self.analysis_config.get('output', {}).get('save_results', True):
                return

            self.logger.info("保存分析结果...")

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存JSON格式的详细结果
            json_file = self.results_path / f"pain_point_analysis_{timestamp}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, ensure_ascii=False, indent=2, default=str)

            # 保存文本格式的摘要报告
            report_file = self.results_path / f"analysis_report_{timestamp}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(self._generate_text_report(analysis_results))

            self.logger.info(f"分析结果已保存到: {json_file} 和 {report_file}")

        except Exception as e:
            self.logger.error(f"保存分析结果失败: {e}")

    def _generate_text_report(self, analysis_results: Dict) -> str:
        """
        生成文本格式的分析报告

        参数:
            analysis_results: 分析结果

        返回:
            文本报告
        """
        report = f"""
# Reddit评论痛点分析报告

## 分析概览
- 分析时间: {analysis_results.get('analysis_time', 'N/A')}
- 评论总数: {analysis_results.get('total_comments', 0)}
- 负面评论数: {analysis_results.get('negative_comments_count', 0)}
- 负面评论比例: {analysis_results.get('negative_ratio', 0):.2%}

## 主要关键词
"""

        keywords = analysis_results.get('top_keywords', [])
        for i, (word, score) in enumerate(keywords[:10], 1):
            report += f"{i}. {word} (重要性: {score:.3f})\n"

        report += "\n## 识别的主题\n"
        topics = analysis_results.get('topic_labels', [])
        for i, topic in enumerate(topics, 1):
            report += f"{i}. {topic}\n"

        report += f"\n## 商业化解决方案\n{analysis_results.get('business_solutions', '暂无生成')}\n"

        return report

    def analyze_pain_points(self, days: int = 7, limit: int = None) -> Dict:
        """
        执行完整的痛点分析流程

        参数:
            days: 分析最近几天的数据
            limit: 限制分析的评论数量

        返回:
            分析结果字典
        """
        try:
            self.logger.info("开始执行痛点分析...")

            # 1. 获取评论数据
            comments = self.get_comments_data(days=days, limit=limit)
            if not comments:
                self.logger.warning("没有获取到评论数据")
                return {}

            # 2. 执行情感分析
            comments = self.perform_sentiment_analysis(comments)

            # 3. 提取关键词
            keywords = self.extract_keywords(comments)

            # 4. 执行主题建模
            topics, topic_labels = self.perform_topic_modeling(comments)

            # 5. 筛选负面评论
            negative_comments = [c for c in comments if c.get('sentiment_label') == 'negative']

            # 6. 构建分析摘要
            analysis_summary = {
                'total_comments': len(comments),
                'negative_comments': negative_comments,
                'negative_comments_count': len(negative_comments),
                'negative_ratio': len(negative_comments) / len(comments) if comments else 0,
                'top_keywords': keywords,
                'topics': topics,
                'topic_labels': topic_labels,
                'analysis_time': datetime.now().isoformat()
            }

            # 7. 生成商业化解决方案
            business_solutions = self.call_ollama_for_business_solutions(analysis_summary)
            analysis_summary['business_solutions'] = business_solutions

            # 8. 生成可视化图表
            self.generate_visualizations(comments, keywords, topic_labels, analysis_summary)

            # 9. 保存分析结果
            self.save_analysis_results(analysis_summary)

            self.logger.info("痛点分析完成")
            return analysis_summary

        except Exception as e:
            self.logger.error(f"痛点分析失败: {e}")
            return {}


def run_pain_point_analysis() -> bool:
    """
    运行痛点分析任务

    返回:
        成功返回True，失败返回False
    """
    try:
        logger.info("开始执行痛点分析任务")

        # 检查是否启用痛点分析
        analysis_config = CONFIG.get('pain_point_analysis', {})
        if not analysis_config.get('enabled', False):
            logger.info("痛点分析功能已禁用，跳过执行")
            return True

        # 检查依赖
        if not ANALYSIS_DEPENDENCIES_AVAILABLE:
            logger.error("数据分析依赖未安装，无法执行痛点分析")
            return False

        # 创建分析器实例
        analyzer = PainPointAnalyzer(CONFIG)

        # 执行分析
        max_comments = analysis_config.get('analysis', {}).get('max_comments_per_batch', 1000)
        results = analyzer.analyze_pain_points(days=7, limit=max_comments)

        if results:
            logger.info("痛点分析任务执行成功")
            logger.info(f"分析了 {results.get('total_comments', 0)} 条评论")
            logger.info(f"识别出 {results.get('negative_comments_count', 0)} 条负面评论")
            logger.info(f"提取了 {len(results.get('top_keywords', []))} 个关键词")
            logger.info(f"识别了 {len(results.get('topic_labels', []))} 个主题")
            return True
        else:
            logger.error("痛点分析任务执行失败")
            return False

    except Exception as e:
        logger.error(f"痛点分析任务执行出错: {str(e)}")
        return False


if __name__ == "__main__":
    main()

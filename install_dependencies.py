#!/usr/bin/env python3
"""
依赖安装脚本 - 针对Python 3.12优化
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败:")
        print(f"错误输出: {e.stderr}")
        return False

def install_basic_packages():
    """安装基础包"""
    basic_packages = [
        "praw>=7.8.0",
        "python-dotenv>=1.0.0", 
        "PyYAML>=6.0.0",
        "schedule>=1.2.0",
        "beautifulsoup4>=4.12.0",
        "requests>=2.31.0",
        "psycopg2-binary>=2.9.0"
    ]
    
    for package in basic_packages:
        if not run_command(f"uv pip install '{package}'", f"安装 {package}"):
            print(f"⚠️  {package} 安装失败，尝试继续...")

def install_data_science_packages():
    """安装数据科学包"""
    # 按依赖顺序安装
    packages_order = [
        ("numpy>=1.24.0", "NumPy数值计算库"),
        ("pandas>=2.0.0", "Pandas数据处理库"),
        ("scikit-learn>=1.3.0", "Scikit-learn机器学习库"),
        ("nltk>=3.8.0", "NLTK自然语言处理库"),
        ("textblob>=0.17.0", "TextBlob文本分析库"),
        ("gensim>=4.3.0", "Gensim主题建模库")
    ]
    
    for package, description in packages_order:
        if not run_command(f"uv pip install '{package}'", f"安装 {description}"):
            print(f"⚠️  {package} 安装失败，尝试使用pip...")
            run_command(f"pip install '{package}'", f"使用pip安装 {description}")

def install_visualization_packages():
    """安装可视化包"""
    viz_packages = [
        ("matplotlib>=3.7.0", "Matplotlib绘图库"),
        ("seaborn>=0.12.0", "Seaborn统计可视化库"),
        ("plotly>=5.17.0", "Plotly交互式图表库")
    ]
    
    for package, description in viz_packages:
        if not run_command(f"uv pip install '{package}'", f"安装 {description}"):
            print(f"⚠️  {package} 安装失败，尝试使用pip...")
            run_command(f"pip install '{package}'", f"使用pip安装 {description}")

def install_wordcloud():
    """特殊处理wordcloud安装"""
    print("\n🔄 安装WordCloud词云库...")
    
    # 尝试多种方式安装wordcloud
    methods = [
        ("uv pip install 'wordcloud>=1.9.3'", "使用uv安装最新版本"),
        ("pip install 'wordcloud>=1.9.3'", "使用pip安装最新版本"),
        ("uv pip install 'wordcloud==1.9.3' --no-build-isolation", "禁用构建隔离"),
        ("pip install 'wordcloud==1.9.3' --no-build-isolation", "使用pip禁用构建隔离"),
        ("conda install -c conda-forge wordcloud", "使用conda安装"),
    ]
    
    for command, description in methods:
        print(f"尝试: {description}")
        if run_command(command, description):
            return True
        print("失败，尝试下一种方法...")
    
    print("❌ WordCloud安装失败，将在代码中添加备选方案")
    return False

def download_nltk_data():
    """下载NLTK数据"""
    print("\n🔄 下载NLTK数据包...")
    
    nltk_downloads = [
        "punkt",
        "stopwords", 
        "wordnet",
        "punkt_tab"
    ]
    
    try:
        import nltk
        for data_name in nltk_downloads:
            try:
                nltk.download(data_name, quiet=True)
                print(f"✅ 下载 {data_name} 成功")
            except Exception as e:
                print(f"⚠️  下载 {data_name} 失败: {e}")
    except ImportError:
        print("❌ NLTK未安装，跳过数据下载")

def create_fallback_requirements():
    """创建备选requirements文件"""
    fallback_content = """# 备选依赖文件 - 最小化版本
# 如果主要安装失败，可以尝试这个文件

# 基础包
praw
python-dotenv
PyYAML
schedule
beautifulsoup4
requests
psycopg2-binary

# 数据分析 (最小版本)
numpy
pandas
scikit-learn
nltk
textblob

# 可视化 (最小版本)
matplotlib
seaborn
plotly

# 如果wordcloud安装失败，可以注释掉下面这行
# wordcloud
"""
    
    with open("requirements_fallback.txt", "w", encoding="utf-8") as f:
        f.write(fallback_content)
    
    print("✅ 创建备选requirements文件: requirements_fallback.txt")

def main():
    """主安装流程"""
    print("🚀 开始安装Reddit痛点分析所需依赖...")
    print(f"Python版本: {sys.version}")
    
    # 检查uv是否可用
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
        print("✅ 检测到uv包管理器")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未检测到uv，请先安装uv或使用pip")
        print("安装uv: curl -LsSf https://astral.sh/uv/install.sh | sh")
        return False
    
    # 分步安装
    print("\n" + "="*50)
    print("第1步: 安装基础包")
    install_basic_packages()
    
    print("\n" + "="*50)
    print("第2步: 安装数据科学包")
    install_data_science_packages()
    
    print("\n" + "="*50)
    print("第3步: 安装可视化包")
    install_visualization_packages()
    
    print("\n" + "="*50)
    print("第4步: 安装WordCloud (可能需要编译)")
    wordcloud_success = install_wordcloud()
    
    print("\n" + "="*50)
    print("第5步: 下载NLTK数据")
    download_nltk_data()
    
    print("\n" + "="*50)
    print("第6步: 创建备选配置")
    create_fallback_requirements()
    
    print("\n🎉 依赖安装完成!")
    
    if not wordcloud_success:
        print("\n⚠️  注意: WordCloud安装失败")
        print("可以尝试以下解决方案:")
        print("1. 安装系统依赖: brew install freetype pkg-config (macOS)")
        print("2. 使用conda: conda install -c conda-forge wordcloud")
        print("3. 程序会自动跳过词云生成功能")
    
    print("\n✅ 现在可以运行痛点分析:")
    print("python src/main.py --analyze")

if __name__ == "__main__":
    main()

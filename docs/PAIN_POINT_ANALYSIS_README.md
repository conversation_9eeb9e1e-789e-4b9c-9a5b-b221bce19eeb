# Reddit评论痛点分析功能

## 功能概述

本功能通过分析Reddit评论数据，识别用户痛点并生成商业化解决方案。主要包括：

1. **数据预处理** - 清理和标准化评论文本
2. **情感分析** - 使用TextBlob识别负面情绪
3. **关键词提取** - 使用TF-IDF算法提取重要词汇
4. **主题建模** - 使用LDA算法识别主要话题
5. **商业化解决方案生成** - 使用Ollama的gemma2:latest模型
6. **可视化分析** - 生成图表和词云

## 安装依赖

### 快速安装（推荐）
```bash
python install_dependencies.py
```

### 手动安装
```bash
# 使用uv（推荐）
uv pip install -r requirements.txt

# 或使用pip
pip install -r requirements.txt
```

**注意**: 如果遇到WordCloud安装问题，请参考 [INSTALL_GUIDE.md](INSTALL_GUIDE.md) 获取详细的安装指南。

## 配置Ollama

1. 安装Ollama：
```bash
# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.ai/install.sh | sh
```

2. 启动Ollama服务：
```bash
ollama serve
```

3. 下载gemma2模型：
```bash
ollama pull gemma2:latest
```

## 配置文件

在 `config/config.yaml` 中已添加痛点分析配置：

```yaml
pain_point_analysis:
  enabled: true
  ollama:
    base_url: "http://localhost:11434"
    model: "gemma2:latest"
    timeout: 60
  analysis:
    min_comment_length: 10
    max_comments_per_batch: 1000
    sentiment_threshold: -0.1
    top_keywords_count: 20
    lda_topics_count: 5
    min_word_frequency: 2
  output:
    save_results: true
    results_path: "data/processed/pain_point_analysis"
    generate_visualizations: true
    charts_path: "data/processed/charts"
```

## 使用方法

### 1. 仅执行痛点分析
```bash
python src/main.py --analyze
```

### 2. 执行爬虫和痛点分析
```bash
python src/main.py --all
```

### 3. 仅执行爬虫
```bash
python src/main.py --now
```

## 输出结果

### 分析报告
- JSON格式详细结果：`data/processed/pain_point_analysis/pain_point_analysis_YYYYMMDD_HHMMSS.json`
- 文本格式摘要报告：`data/processed/pain_point_analysis/analysis_report_YYYYMMDD_HHMMSS.txt`

### 可视化图表
- 情感分布图：`data/processed/charts/sentiment_distribution.png`
- 关键词词云：`data/processed/charts/keywords_wordcloud.png`
- 主题分布图：`data/processed/charts/topic_distribution.png`
- 情感时间序列：`data/processed/charts/sentiment_timeline.png`

## 分析流程

1. **数据获取**：从数据库获取最近7天的评论数据
2. **文本预处理**：清理文本、分词、去停用词、词形还原
3. **情感分析**：计算情感极性和主观性，分类为积极/中性/消极
4. **关键词提取**：使用TF-IDF算法提取重要词汇
5. **主题建模**：使用LDA算法识别主要话题
6. **商业化方案生成**：调用Ollama模型生成解决方案
7. **可视化**：生成各种图表展示分析结果
8. **结果保存**：保存JSON和文本格式的分析报告

## 核心类和方法

### PainPointAnalyzer类

主要方法：
- `analyze_pain_points()` - 执行完整分析流程
- `preprocess_text()` - 文本预处理
- `perform_sentiment_analysis()` - 情感分析
- `extract_keywords()` - 关键词提取
- `perform_topic_modeling()` - 主题建模
- `call_ollama_for_business_solutions()` - 生成商业化解决方案
- `generate_visualizations()` - 生成可视化图表

### 独立函数

- `run_pain_point_analysis()` - 运行痛点分析任务的入口函数

## 注意事项

1. 确保Ollama服务正在运行且gemma2模型已下载
2. 确保数据库中有足够的评论数据
3. 首次运行时会自动下载NLTK数据包
4. 生成的图表需要支持中文字体的环境

## 故障排除

### 常见问题

1. **ImportError**: 安装所需依赖 `pip install -r requirements.txt`
2. **Ollama连接失败**: 检查Ollama服务是否启动，端口是否正确
3. **NLTK数据下载失败**: 手动下载 `python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"`
4. **中文字体显示问题**: 安装中文字体或修改matplotlib配置

### 日志查看

查看详细日志：
```bash
tail -f logs/main.log
```

## 扩展功能

可以通过修改配置文件来调整：
- 分析的时间范围
- 评论数量限制
- 情感阈值
- 关键词数量
- 主题数量
- Ollama模型参数

## API集成

该功能也可以作为独立模块在其他Python项目中使用：

```python
from src.main import PainPointAnalyzer, CONFIG

analyzer = PainPointAnalyzer(CONFIG)
results = analyzer.analyze_pain_points(days=7, limit=1000)
print(results)
```

# 依赖安装指南

## 快速安装

### 方法1: 使用自动安装脚本（推荐）
```bash
python install_dependencies.py
```

### 方法2: 使用uv包管理器
```bash
uv pip install -r requirements.txt
```

### 方法3: 使用传统pip
```bash
pip install -r requirements.txt
```

## 分步安装（如果上述方法失败）

### 1. 安装基础依赖
```bash
uv pip install praw python-dotenv PyYAML schedule beautifulsoup4 requests psycopg2-binary
```

### 2. 安装数据科学库
```bash
# 核心数据处理
uv pip install numpy pandas

# 机器学习
uv pip install scikit-learn

# 自然语言处理
uv pip install nltk textblob gensim
```

### 3. 安装可视化库
```bash
# 基础绘图
uv pip install matplotlib seaborn plotly

# 词云（可能需要编译，可选）
uv pip install wordcloud
```

## 解决WordCloud安装问题

WordCloud在某些系统上可能需要编译，如果安装失败：

### macOS
```bash
# 安装系统依赖
brew install freetype pkg-config

# 然后重新安装
uv pip install wordcloud
```

### Ubuntu/Debian
```bash
# 安装系统依赖
sudo apt-get install python3-dev libfreetype6-dev pkg-config

# 然后重新安装
uv pip install wordcloud
```

### 使用conda（推荐用于WordCloud）
```bash
conda install -c conda-forge wordcloud
```

### 跳过WordCloud
如果WordCloud安装仍然失败，可以注释掉requirements.txt中的wordcloud行：
```
# wordcloud>=1.9.3
```

程序会自动检测并使用条形图替代词云功能。

## 验证安装

运行以下命令验证安装：
```bash
python -c "
import pandas, numpy, sklearn, nltk, textblob, matplotlib, seaborn
print('✅ 核心依赖安装成功')

try:
    import wordcloud
    print('✅ WordCloud安装成功')
except ImportError:
    print('⚠️  WordCloud未安装，将使用备选方案')
"
```

## 下载NLTK数据

首次运行时会自动下载，也可以手动下载：
```bash
python -c "
import nltk
nltk.download('punkt')
nltk.download('stopwords') 
nltk.download('wordnet')
print('✅ NLTK数据下载完成')
"
```

## 配置Ollama

### 1. 安装Ollama
```bash
# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# 从 https://ollama.ai 下载安装包
```

### 2. 启动Ollama服务
```bash
ollama serve
```

### 3. 下载模型
```bash
ollama pull gemma2:latest
```

### 4. 验证Ollama
```bash
curl http://localhost:11434/api/tags
```

## 常见问题

### Q: uv命令不存在
A: 安装uv包管理器：
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### Q: Python版本兼容性
A: 确保使用Python 3.8+，推荐Python 3.10+

### Q: 权限错误
A: 使用虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate  # Windows
```

### Q: 网络问题
A: 使用国内镜像：
```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 最小化安装

如果只需要基本功能，可以只安装：
```bash
uv pip install praw python-dotenv PyYAML schedule beautifulsoup4 requests psycopg2-binary pandas numpy scikit-learn nltk textblob matplotlib
```

这将跳过词云、高级可视化和主题建模功能。

## 测试安装

安装完成后，测试痛点分析功能：
```bash
python src/main.py --analyze
```

如果看到"数据分析依赖未安装"的警告，请检查上述安装步骤。

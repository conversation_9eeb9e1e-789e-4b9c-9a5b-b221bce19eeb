paths:
  cookies_path: "data/cookies/cookies.json"
  raw_data: "data/raw"
  processed_data: "data/processed"
  logs: "logs/parser.log"

schedule:
  daily_time: "02:00"  # 每天执行爬虫的时间

logging:
  config_file: "config/logging.yaml"

# Reddit 爬虫配置
reddit:
  enabled: true
  subreddits:
    - apple
    - Entrepreneur
    - startups
    - smallbusiness
    - EntrepreneurRideAlong
    - InternetIsBeautiful
    - SideProject
    - SaaS
    - indiehackers
    - indiebiz
    - IMadeThis
    - alphaandbetausers
    - roastmystartup
    - AppBusiness
    - AppHookup
    - design_critiques
    - iosapps
    - copywriting
    - advertising
    - marketing
    - digital_marketing
    - socialmedia
    - SocialMediaMarketing
    - Instagram
    - InstagramMarketing
    - Blogging
    - ProductivityApps
    - productivity
    - webdev
    - GrowthHacking
    - web_design
    - writing
    - Twitter
    - apps
    - IPhoneApps
    - AppDevelopers
    - Startup_Ideas
  posts_limit: 5
  comments_limit: 10
  rankings:
    - hot
    - new
    - top
  time_filters:
    top: week
    controversial: day
  batch_size: 5  # 每批处理的子版块数量
  delay_between_subreddits: 3  # 每个子版块处理完后的延迟（秒）
  delay_between_batches: 10  # 每批处理完后的延迟（秒）
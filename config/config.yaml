paths:
  cookies_path: "data/cookies/cookies.json"
  raw_data: "data/raw"
  processed_data: "data/processed"
  logs: "logs/parser.log"

schedule:
  daily_time: "02:00"  # 每天执行爬虫的时间

logging:
  config_file: "config/logging.yaml"

# Reddit 爬虫配置
reddit:
  enabled: true
  subreddits:
    - apple
    - Entrepreneur
    - startups
    - smallbusiness
    - EntrepreneurRideAlong
    - InternetIsBeautiful
    - SideProject
    - SaaS
    - indiehackers
    - indiebiz
    - IMadeThis
    - alphaandbetausers
    - roastmystartup
    - AppBusiness
    - AppHookup
    - design_critiques
    - iosapps
    - copywriting
    - advertising
    - marketing
    - digital_marketing
    - socialmedia
    - SocialMediaMarketing
    - Instagram
    - InstagramMarketing
    - Blogging
    - ProductivityApps
    - productivity
    - webdev
    - GrowthHacking
    - web_design
    - writing
    - Twitter
    - apps
    - IPhoneApps
    - AppDevelopers
    - Startup_Ideas
  posts_limit: 5
  comments_limit: 10
  rankings:
    - hot
    - new
    - top
  time_filters:
    top: week
    controversial: day
  batch_size: 5  # 每批处理的子版块数量
  delay_between_subreddits: 3  # 每个子版块处理完后的延迟（秒）
  delay_between_batches: 10  # 每批处理完后的延迟（秒）

# 痛点分析配置
pain_point_analysis:
  enabled: true
  ollama:
    base_url: "http://localhost:11434"  # Ollama服务地址
    model: "gemma2:latest"  # 使用的模型
    timeout: 60  # 请求超时时间（秒）
  analysis:
    min_comment_length: 10  # 最小评论长度
    max_comments_per_batch: 1000  # 每批分析的最大评论数
    sentiment_threshold: -0.1  # 负面情感阈值
    top_keywords_count: 20  # 提取的关键词数量
    lda_topics_count: 5  # LDA主题数量
    min_word_frequency: 2  # 最小词频
  output:
    save_results: true  # 是否保存分析结果
    results_path: "data/processed/pain_point_analysis"  # 结果保存路径
    generate_visualizations: true  # 是否生成可视化图表
    charts_path: "data/processed/charts"  # 图表保存路径